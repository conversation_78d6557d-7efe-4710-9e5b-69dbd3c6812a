import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  View,
  Text,
  useWindowDimensions,
  TouchableOpacity,
  Image,
  StyleSheet,
  FlatList,
  TextInput,
  ActivityIndicator,
  Alert,
} from "react-native";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useAuth } from "../../../../context/auth-context";
import { router, useFocusEffect, useRouter } from 'expo-router';
import { transformUrl } from "../../../../utils/transformUrl";
import PhotoGrid from "../../../../components/Smalls/PhotoGrid";
import LabModal from "../../../../components/Larges/LabModal";
import CourtModal from "../../../../components/Larges/CourtModal";
import SuccessScreen from '../../../../components/Smalls/SuccessScreen';
import { Colors } from "../../../../constants/colors";
import { apiService } from "../../../../services/api";

const Tab1 = ({ caseid, changeTab }) => {
  const { width } = useWindowDimensions();
  const [premisesImages, setPremisesImages] = useState([]);
  const [caseDetails, setCaseDetails] = useState(null);
  const [evidences, setEvidences] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { token } = useAuth();  
  // console.log(token);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isCourtModalVisible, setIsCourtModalVisible] = useState(false);
  const [currentEvidenceId, setCurrentEvidenceId] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedLabId, setSelectedLabId] = useState(null);
  const [selectedDepartmentId, setSelectedDepartmentId] = useState(null);
  const [selectedLabName, setSelectedLabName] = useState(null);
  const [selectedDepartmentName, setSelectedDepartmentName] = useState(null);

  // Selection functionality
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedEvidenceIds, setSelectedEvidenceIds] = useState([]);
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);
  const [forensicQr, setForensicQr] = useState(null);

  const fetchData = useCallback(async () => {
    if (!caseid || !token) {
      setIsLoading(false);
      setError('Missing case ID or authentication token');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await apiService.fetchCaseDetails(token, caseid);

      if (!result?.data) {
        throw new Error('Invalid data format received from server');
      }

      const premisesImageUrls = result.data.premisesImageUrl || [];
      const transformedImages = premisesImageUrls.map(url => 
        typeof url === 'string' ? transformUrl(url) : null
      ).filter(Boolean);
      
      setCaseDetails(result.data);
      setPremisesImages(transformedImages);
      setEvidences(result.data.evidences || []);
    } catch (err) {
      console.error('Error fetching case data:', err);
      setError(err.message || 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [caseid, token]);

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [fetchData])
  );

  useEffect(() => {
    setIsSelectionMode(false);
    setSelectedEvidenceIds([]);
  }, [evidences]);

  const handleSelectionConfirm = useCallback(async (labDepartmentData) => {
    if (!currentEvidenceId) {
      Alert.alert('Error', 'No evidence selected');
      return;
    }

    if (!caseid || !token) {
      Alert.alert('Error', 'Missing case ID or authentication token');
      return;
    }

    setIsSubmitting(true);

    try {
      let updateData;

      // Check if it's the new lab_department array format
      if (Array.isArray(labDepartmentData) && labDepartmentData.length > 0 && labDepartmentData[0].labId) {
        // New multi-select format with priorities
        updateData = {
          lab_department: labDepartmentData.map(combo => ({
            labId: combo.labId,
            labDepartmentId: combo.labDepartmentId,
            priority: combo.priority
          }))
        };

        console.log('Multi-select lab-department combinations confirmed:', updateData);

        // Update UI state with first combination for display
        const firstCombo = labDepartmentData[0];
        setSelectedLabId(firstCombo.labId);
        setSelectedDepartmentId(firstCombo.labDepartmentId);
        setSelectedLabName(firstCombo.labName);
        setSelectedDepartmentName(firstCombo.departmentName);

      } else {
        // Legacy single select format (backward compatibility)
        const labId = labDepartmentData;
        const departmentId = arguments[1];
        const labName = arguments[2];
        const departmentName = arguments[3];

        updateData = {
          labId: labId,
          labDepartmentId: departmentId
        };

        setSelectedLabId(labId);
        setSelectedDepartmentId(departmentId);
        setSelectedLabName(labName);
        setSelectedDepartmentName(departmentName);

        console.log('Single select confirmed:', updateData);
      }

      const result = await apiService.updateCaseEvidence(token, caseid, currentEvidenceId, updateData);

      Alert.alert('Success!', 'The evidence has been successfully shared with the lab(s).');
      setIsModalVisible(false);
      fetchData();
    } catch (err) {
      console.error('Error sharing evidence:', err);
      Alert.alert('Error', err.message || 'Failed to share evidence with lab');
    } finally {
      setIsSubmitting(false);
    }
  }, [currentEvidenceId, caseid, token, fetchData]);

  const handleEvidenceClick = useCallback((evidenceId) => {
    if (isSelectionMode) {
      handleSelectEvidence(evidenceId);
    } else if (evidenceId) {
      router.push({
        pathname: '(screens)/evidenceDetails',
        params: { evidenceId, caseid },
      });
    }
  }, [isSelectionMode, caseid]);

  const handleAddEvidenceClick = useCallback(() => {
    if (!caseid) {
      Alert.alert('Error', 'Missing case ID');
      return;
    }
    
    router.push({
      pathname: '(screens)/captureEvidences',
      params: { caseid },
    });
  }, [caseid]);

  const handleReportClick = useCallback(() => {
    if (typeof changeTab === 'function') {
      changeTab('tab2');
    }
  }, [changeTab]);

  const handleShareButtonClick = useCallback((evidenceId) => {
    if (!evidenceId) {
      Alert.alert('Error', 'No evidence selected');
      return;
    }
    
    setCurrentEvidenceId(evidenceId);
    setIsModalVisible(true);
  }, []);

  const handleLongPress = useCallback((evidenceId, hasLabInfo) => {
    if (!evidenceId) return;
    
    if (hasLabInfo) {
      setIsSelectionMode(true);
      setSelectedEvidenceIds([evidenceId]);
    } else {
      Alert.alert('Cannot Select', 'Only evidences with lab and department information can be selected.');
    }
  }, []);

  const handleSelectEvidence = useCallback((evidenceId) => {
    if (!evidenceId) return;
    
    const evidence = evidences.find(evidence => evidence._id === evidenceId);
    if (!evidence) return;
    
    const hasLabInfo = !!(evidence.labId && evidence.labDepartmentId);

    if (!hasLabInfo) {
      Alert.alert('Cannot Select', 'Only evidences with lab and department information can be selected.');
      return;
    }

    setSelectedEvidenceIds(prevSelected => {
      if (prevSelected.includes(evidenceId)) {
        const newSelected = prevSelected.filter(id => id !== evidenceId);
        if (newSelected.length === 0) {
          setIsSelectionMode(false);
        }
        return newSelected;
      } else {
        return [...prevSelected, evidenceId];
      }
    });
  }, [evidences]);

  const handleCancelSelection = useCallback(() => {
    setIsSelectionMode(false);
    setSelectedEvidenceIds([]);
  }, []);

  const handleCourtSelectionConfirm = useCallback(async (courtId, courtName) => {
    if (!caseid || !token) {
      Alert.alert('Error', 'Missing case ID or authentication token');
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await apiService.submitCaseToCourt(token, caseid, courtId, null);
 

      Alert.alert('Success!', 'The case has been successfully submitted to the court.');
      setIsCourtModalVisible(false);
      fetchData();
    } catch (err) {
      console.error('Error submitting case to court:', err);
      Alert.alert('Error', err.message || 'Failed to submit case to court');
    } finally {
      setIsSubmitting(false);
    }
  }, [caseid, token, fetchData]);

  const handleSubmitToJudiciary = useCallback(() => {
    if (isSelectionMode) {
      handleCancelSelection();
    } else {
      setIsCourtModalVisible(true);
    }
  }, [isSelectionMode, handleCancelSelection]);

  const handleProcessSelected = useCallback(async () => {
    if (selectedEvidenceIds.length === 0) {
      Alert.alert('Error', 'No evidences selected');
      return;
    }

    if (!caseid || !token) {
      Alert.alert('Error', 'Missing case ID or authentication token');
      return;
    }
    
    setIsSubmitting(true);

    try {
      const result = await apiService.submitCaseToForensic(token, caseid, selectedEvidenceIds);
      
      const qrCode = result?.data?.[0]?.qrCode || null;
      
      setForensicQr(qrCode);
      setSelectedEvidenceIds([]);
      setIsSelectionMode(false);
      setIsSuccessVisible(true);
    } catch (err) {
      console.error("Error submitting evidence:", err);
      Alert.alert('Error', err.message || 'Failed to submit evidence to forensic');
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedEvidenceIds, caseid, token]);

  const renderEvidenceMedia = useCallback((url) => {
    if (!url) return <View style={styles.evidenceImage} />;
    

    const transformedUrl = transformUrl(url);


    const isVideo = /\.(mp4|mov|avi|wmv|flv|webm|mkv)$/i.test(url);

    if (isVideo) {
      return (
        <View style={styles.evidenceImage}>
          <MaterialCommunityIcons name="play-circle" size={30} color={Colors.background} style={styles.playIcon} />
          <View style={styles.videoOverlay}>
            <Text style={styles.videoLabel}>VIDEO</Text>
          </View>
        </View>
      );
    } else {

      return (
        <Image
          source={{ uri: transformedUrl }}
          style={styles.evidenceImage}
          defaultSource={require('../../../../assets/images/small_satya_smadhanLogo.png')}
        />
      );
    }
  }, []);

  const numColumns = width < 600 ? 1 : 3;

  const renderItem = useCallback(({ item }) => {
    switch (item.type) {
      case 'premisesImages':
  
      
        // Check if premisesImages is empty or undefined
        if (!premisesImages || premisesImages.length === 0) {
          return (
            <View style={styles.sectionContainer}>
              <TouchableOpacity
                style={styles.addButton}
                onPress={() =>
                  router.push({
                    pathname: '(screens)/premisesCapture',
                    params: { caseId: caseid },
                  })
                } // Replace with your target route
              >
                <Text style={styles.addButtonText}>Add Premises Image</Text>
              </TouchableOpacity>
            </View>
          );
        }
        return (
          <View style={styles.sectionContainer}>
            <PhotoGrid 
              images={premisesImages}
              description={caseDetails?.premiseDescription || 'No description available'} 
              label='Premises Description'
            />
          </View>
        );


      case 'details':
        return (
          <View style={styles.detailsContainer}>
            <View style={styles.segmentedControlContainer}>
              <View style={styles.segmentedControl}>
                <TouchableOpacity
                  style={[styles.segmentButton, styles.activeSegment]}
                >
                  <MaterialCommunityIcons name="file-document-multiple-outline" size={18} color={Colors.background} />
                  <Text style={styles.activeSegmentText}>Evidences</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.segmentButton}
                  onPress={handleReportClick}
                >
                  <MaterialCommunityIcons name="clipboard-text-outline" size={18} color={Colors.lightText} />
                  <Text style={styles.segmentText}>Reports</Text>
                </TouchableOpacity>
              </View>
            </View>
            {[
              { label: 'Created By', value: caseDetails?.createdBy?.name || 'N/A' },
              { label: 'Created At', value: caseDetails?.createdAt ? new Date(caseDetails.createdAt).toLocaleString() : 'N/A' },
              { label: 'Title', value: caseDetails?.title || 'N/A' },
              { label: 'Description', value: caseDetails?.description || 'N/A' },
              { label: 'Fir Number', value: caseDetails?.firNumber || 'N/A' },
              { label: 'Case Type', value: caseDetails?.caseType || 'N/A' },
              { label: 'Address 1', value: caseDetails?.address1 || 'N/A' },
              { label: 'Address 2', value: caseDetails?.address2 || 'N/A' },
              { label: 'State', value: caseDetails?.state || 'N/A' },
              { label: 'City', value: caseDetails?.city || 'N/A' },
              { label: 'Pincode', value: caseDetails?.pincode || 'N/A' },
              { label: 'GPS Location', value: caseDetails?.gpsLocation || 'N/A' },
              { label: 'Remarks', value: caseDetails?.remarks || 'N/A' },
            ].map((detail, index) => (
              <View style={styles.detailRow} key={index}>
                <Text style={styles.label}>{detail.label}</Text>
                <Text style={styles.value}>{detail.value}</Text>
              </View>
            ))}
          </View>
        );
      case 'evidences':
        return (
          <>
         
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>
              {isSelectionMode
                ? `Evidences (${selectedEvidenceIds.length} selected)`
                : 'Evidences'}
            </Text>
            {evidences && evidences.length > 0 ? (
              evidences.map((evidence, index) => {
                if (!evidence) return null;
                
                const firstAttachment = evidence.attachmentUrl?.[0] || null;
                const hasLabInfo = !!(evidence.labId && evidence.labDepartmentId);
                const isSelected = selectedEvidenceIds.includes(evidence._id);

                return (
                  <TouchableOpacity
                    key={evidence._id || index}
                    style={[
                      styles.evidenceCard,
                      isSelected && styles.selectedEvidenceCard
                    ]}
                    onPress={() => handleEvidenceClick(evidence._id)}
                    onLongPress={() => handleLongPress(evidence._id, hasLabInfo)}
                    delayLongPress={300}
                    disabled={isSubmitting}
                  >
                    {isSelected && (
                      <View style={styles.selectedIndicator}>
                        <MaterialCommunityIcons name="check-circle" size={24} color={Colors.primary} />
                      </View>
                    )}
                    <View style={styles.imageContainer}>
                      {renderEvidenceMedia(firstAttachment)}
                    </View>
                    <View style={styles.evidenceDetails}>
                      <Text style={styles.evidenceTitle}>{evidence.title || 'Untitled Evidence'}</Text>
                      <Text style={styles.evidenceLabel}>
                        Type: <Text style={styles.evidenceType}>{evidence.type || 'Unknown'}</Text>
                      </Text>

                      {evidence.labId && (
                        <Text style={styles.evidenceLabel}>
                          Lab: <Text style={styles.evidenceType}>
                            {typeof evidence.labId === 'object' ? evidence.labId.name : 'Unknown Lab'}
                          </Text>
                        </Text>
                      )}
                      {evidence.labDepartmentId && (
                        <Text style={styles.evidenceLabel}>
                          Department: <Text style={styles.evidenceType}>
                            {typeof evidence.labDepartmentId === 'object' ? evidence.labDepartmentId.name : 'Unknown Department'}
                          </Text>
                        </Text>
                      )}
                    </View>
                    {!isSelectionMode && (
                      <TouchableOpacity
                        style={styles.shareButton}
                        onPress={() => handleShareButtonClick(evidence._id)}
                        disabled={isSubmitting}
                      >
                        <MaterialCommunityIcons
                          name="share-circle"
                          size={24}
                          color={evidence.labDepartmentId ? "#4CAF50" : Colors.primary}
                        />
                      </TouchableOpacity>
                    )}
                  </TouchableOpacity>
                );
              })
            ) : (
              <Text style={styles.noEvidenceText}>No evidences found</Text>
            )}
          </View>
          </>
        );
      case 'addButton':
        return (
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddEvidenceClick}
            disabled={isSelectionMode || isSubmitting}
          >
            <MaterialCommunityIcons 
              name="plus" 
              size={20} 
              color={(isSelectionMode || isSubmitting) ? Colors.disabled : Colors.primary} 
            />
            <Text style={[
              styles.addButtonText, 
              (isSelectionMode || isSubmitting) && styles.disabledText
            ]}>
              Add New Evidence
            </Text>
          </TouchableOpacity>
        );
      case 'commentSection':
        return (
          <>
          <Text style={styles.sectionTitle}>Add Comment</Text>
          <View style={styles.commentSection}>
          
            <TextInput
              style={styles.commentInput}
              placeholder="Type your comment here..."
              multiline
              editable={!isSelectionMode && !isSubmitting}
            />
            <TouchableOpacity
              style={[
                styles.commentButton, 
                (isSelectionMode || isSubmitting) && styles.disabledButton
              ]}
              disabled={isSelectionMode || isSubmitting}
            >
              <Text style={styles.commentButtonText}>Add Comment</Text>
            </TouchableOpacity>
            <View style={styles.commentsDisplay}>
              <Text style={styles.commentsTitle}>Comments</Text>
              <View style={styles.commentItem}>
                <Image 
                  style={styles.userAvatar} 
                  // source={require('../../../../assets/avatar.png')}
                />
                <View style={styles.commentContent}>
                  <Text style={styles.userName}>Sourabh (Police Officer)</Text>
                  <Text style={styles.commentText}>The Evidences are been uploaded</Text>
                </View>
              </View>
              <View style={styles.commentItem}>
                <Image 
                  style={styles.userAvatar}
                  // source={require('../../../../assets/avatar.png')}
                />
                <View style={styles.commentContent}>
                  <Text style={styles.userName}>Vashkarjya (Investigating Officer)</Text>
                  <Text style={styles.commentText}>Okey, good work</Text>
                </View>
              </View>
            </View>
          </View>
          </>
        );
      case 'Buttons':
        return (
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.button, 
                isSelectionMode ? { backgroundColor: '#FF3B30' } : styles.button
              ]}
              onPress={handleSubmitToJudiciary}
              disabled={isSubmitting}
            >
              <Text style={styles.buttonText}>
                {isSelectionMode ? 'CANCEL' : 'SUBMIT TO JUDICIARY'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.button,
                (selectedEvidenceIds.length === 0 || isSubmitting) && styles.disabledButton
              ]}
              onPress={handleProcessSelected}
              disabled={selectedEvidenceIds.length === 0 || isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color={Colors.background} />
              ) : (
                <Text style={styles.buttonText}>
                  SUBMIT TO FORENSIC
                </Text>
              )}
            </TouchableOpacity>
          </View>
        );
      default:
        return null;
    }
  }, [
    premisesImages, 
    caseDetails, 
    evidences, 
    isSelectionMode, 
    selectedEvidenceIds, 
    isSubmitting,
    handleReportClick,
    handleAddEvidenceClick,
    handleEvidenceClick,
    handleLongPress,
    handleShareButtonClick,
    handleCancelSelection,
    handleProcessSelected,
    renderEvidenceMedia
  ]);

  // Memoized data list to prevent unnecessary re-renders
  const data = useMemo(() => [
    { type: 'premisesImages' },
    { type: 'details' },
    { type: 'evidences' },
    { type: 'addButton' },
    { type: 'commentSection' },
    { type: 'Buttons' },
  ], []);

  // Error display
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchData}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Full-screen loader
  if (isLoading) {
    return (
      <View style={styles.fullScreenLoader}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.type}-${index}`}
          contentContainerStyle={{ flexGrow: 1 }}
          initialNumToRender={3}
          maxToRenderPerBatch={5}
          windowSize={5}
        />
      </View>

      <LabModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onConfirmSelection={handleSelectionConfirm}
        isSubmitting={isSubmitting}
      />

      <CourtModal
        isVisible={isCourtModalVisible}
        onClose={() => setIsCourtModalVisible(false)}
        onConfirmSelection={handleCourtSelectionConfirm}
        isSubmitting={isSubmitting}
      />

      {isSuccessVisible && (
        <SuccessScreen
          message="Evidence data synced successfully!"
          duration={2000}
          onComplete={() => {
            setIsSuccessVisible(false);
            if (forensicQr) {
              router.replace({
                pathname: '(screens)/forensicQr',
                params: {
                  caseid: caseid,
                  ForensicQr: forensicQr,
                },
                resetHistory: true
              });
            } else {
              fetchData();
            }
          }}
        />
      )}
    </GestureHandlerRootView>
  );
};

export default React.memo(Tab1);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  detailsContainer: {
    paddingHorizontal: '2%',
    paddingVertical: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  label: {
    fontSize: 14,
    opacity: 0.5,
    width: '40%',
    fontFamily: 'Roboto',
  },
  value: {
    fontSize: 14,
    width: '60%',
    fontWeight: '450',
    fontFamily: 'Roboto',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  addButtonText: {
    color: Colors.primary,
    fontSize: 18,
    marginLeft: 8,
    fontWeight: 'bold',
    fontFamily: 'Roboto_Bold',
  },
  disabledText: {
    color: Colors.disabled,
    fontFamily: 'Roboto',
  },
  sectionContainer: {
    marginTop: 20,
  },
  sectionTitle: {
    textAlign: 'left',
    color: Colors.primary,
    fontWeight: '500',
    fontSize: 18,
    marginBottom: 14,
    marginHorizontal: '3%',
    fontFamily: 'Roboto_bold',
  },
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginHorizontal: '2%',
    marginBottom: 24,
    padding: 12,
    borderRadius: 10,
    flexDirection: 'row',
    position: 'relative',
  },
  selectedEvidenceCard: {
    borderColor: Colors.primary,
    borderWidth: 2,
    backgroundColor: 'rgba(11, 54, 161, 0.05)',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1,
  },
  imageContainer: {
    width: 80,
    marginRight: 12,
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  evidenceDetails: {
    flex: 1,
  },
  evidenceLabel: {
    fontSize: 12,
    color: Colors.lightText,
    marginBottom: 4,
  },
  evidenceDescription: {
    fontWeight: '500',
    marginBottom: 8,
    flexShrink: 1,
  },
  evidenceType: {
    fontSize: 12,
    color: Colors.lightText,
  },
  evidenceTitle: {
    fontSize: 15,
    color: Colors.black,
  },
  shareButton: {
    padding: 4,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.background,
  },
  errorText: {
    color: '#FF3B30',
    marginVertical: 16,
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
  commentSection: {
    marginHorizontal: '2%',
    marginTop: 0,
  },
  commentInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    marginBottom: 16,
    fontSize: 14,
  },
  commentButton: {
    backgroundColor: Colors.primary,
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
    marginBottom: 16,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
    opacity: 0.7,
  },
  commentButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
  commentsDisplay: {
    marginTop: 16,
  },
  commentsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  commentItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    backgroundColor: '#f0f0f0',
  },
  commentContent: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    fontFamily: 'Roboto',
  },
  commentText: {
    fontSize: 14,
    color: Colors.black,
    fontFamily: 'Roboto',
  },
  imageGrid: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 100,
    margin: 5,
    borderRadius: 8,
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  segmentedControlContainer: {
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 30,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#E8E8E8',
  },
  segmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    justifyContent: 'center',
    width: 150,
  },
  activeSegment: {
    backgroundColor: '#367E18',
    borderRadius: 30,
  },
  segmentText: {
    color: '#666666',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  activeSegmentText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  noEvidenceText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 40,
    fontFamily: 'Roboto',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: '2%',
    marginTop: 16,
    marginBottom: 24,
  },
  button: {
    flex: 1,
    backgroundColor: '#0B36A1',
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  }
});