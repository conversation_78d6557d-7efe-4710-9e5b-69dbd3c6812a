import React, { useState, useEffect, useCallback, useRef } from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet, ActivityIndicator, Animated } from "react-native";
import Constants from 'expo-constants';
import { Colors } from "../../constants/colors";
import { GestureHandlerRootView, PanGestureHandler } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

// Enhanced error logger
const logError = (context, error) => {
  const errorMessage = `ERROR [${context}]: ${error.message || error}`;
  
  console.log('\n' + '='.repeat(80));
  console.log(errorMessage);
  console.log('Stack:', error.stack || 'No stack trace available');
  
  if (error.response) {
    console.log('Response data:', error.response.data);
    console.log('Response status:', error.response.status);
  }
  
  if (error.request) {
    console.log('Request:', error.request);
  }
  
  if (error.config) {
    console.log('Request URL:', error.config.url);
    console.log('Request method:', error.config.method);
  }
  
  console.log('='.repeat(80) + '\n');
  
  return errorMessage;
};

const LabSelectionModal = ({
  isVisible,
  onClose,
  onConfirmSelection,
}) => {
  const [labs, setLabs] = useState([]);
  const [departments, setDepartments] = useState([]);

  // Multi-select state
  const [selectedLabs, setSelectedLabs] = useState([]);
  const [selectedDepartments, setSelectedDepartments] = useState([]);
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);

  // Lab-Department combinations with priorities (for new API structure)
  const [labDepartmentCombinations, setLabDepartmentCombinations] = useState([]);

  // Legacy single select state (for backward compatibility)
  const [selectedLab, setSelectedLab] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState(null);

  const [errors, setErrors] = useState({});
  const [currentStep, setCurrentStep] = useState('lab');
  const [isLoading, setIsLoading] = useState(false);

  // Drag and drop state
  const [isDragging, setIsDragging] = useState(false);
  const [draggedItem, setDraggedItem] = useState(null);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);

  // Animation values
  const dragY = useRef(new Animated.Value(0)).current;
  const itemRefs = useRef({});

  const fetchLabs = useCallback(() => {
    if (!BASE_URL) {
      const errorMsg = logError('Environment', 'BASE_URL is undefined. Check your environment configuration.');
      setErrors(prev => ({ ...prev, baseUrl: errorMsg }));
      return;
    }
    
    setIsLoading(true);
    console.log(`Fetching labs from: ${BASE_URL}/api/forensic/lab`);
    
    fetch(`${BASE_URL}/api/forensic/lab`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Labs data received:', JSON.stringify(data, null, 2));
        if (data.status === "success") {
          setLabs(data.data);
        } else {
          throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
        }
      })
      .catch((error) => {
        const errorMsg = logError('Fetching Labs', error);
        setErrors(prev => ({ ...prev, labsFetch: errorMsg }));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Fetch departments for the selected lab - using useCallback
  const fetchDepartments = useCallback((labId) => {
    if (!labId) {
      const errorMsg = logError('Fetch Departments', 'No lab ID provided');
      setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
      return;
    }
    
    setIsLoading(true);
    console.log(`Fetching departments for lab ID ${labId} from: ${BASE_URL}/api/forensic/lab/${labId}/department`);
    
    fetch(`${BASE_URL}/api/forensic/lab/${labId}/department`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Departments data received:', JSON.stringify(data, null, 2));
        if (data.status === "success") {
          setDepartments(data.data);
          setCurrentStep('department');
        } else {
          throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
        }
      })
      .catch((error) => {
        const errorMsg = logError('Fetching Departments', error);
        setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
        // Even in case of error, switch to department step to show error
        setCurrentStep('department');
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);
  
  // Prefetch labs when the modal becomes visible
  useEffect(() => {
    if (isVisible) {
      setCurrentStep('lab');
      fetchLabs();
    } else {
      // Reset state when modal is closed
      setSelectedLab(null);
      setSelectedDepartment(null);
      setSelectedLabs([]);
      setSelectedDepartments([]);
      setLabDepartmentCombinations([]);
      setIsMultiSelectMode(false);
      setErrors({});
      setIsDragging(false);
      setDraggedItem(null);
    }
  }, [isVisible, fetchLabs]);

  // Handle lab selection
  const handleLabSelect = (lab) => {
    if (isDragging) return;

    if (isMultiSelectMode) {
      // Multi-select mode: toggle selection
      setSelectedLabs(prevSelected => {
        const isAlreadySelected = prevSelected.some(selectedLab => selectedLab._id === lab._id);
        if (isAlreadySelected) {
          return prevSelected.filter(selectedLab => selectedLab._id !== lab._id);
        } else {
          return [...prevSelected, lab];
        }
      });
    } else {
      // Single select mode: proceed to departments
      console.log('Lab selected:', lab.name, 'ID:', lab._id);
      setSelectedLab(lab);
      fetchDepartments(lab._id);
    }
  };

  // Handle long press on lab to enter multi-select mode
  const handleLabLongPress = (lab) => {
    if (isDragging) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setIsMultiSelectMode(true);
    setSelectedLabs([lab]);
    console.log('Entered multi-select mode with lab:', lab.name);
  };

  // Handle department selection
  const handleDepartmentSelect = (department) => {
    if (isDragging) return;

    if (isMultiSelectMode) {
      // Multi-select mode: create lab-department combinations
      if (selectedLab) {
        setLabDepartmentCombinations(prevCombinations => {
          const existingIndex = prevCombinations.findIndex(
            combo => combo.labId === selectedLab._id && combo.labDepartmentId === department._id
          );

          if (existingIndex >= 0) {
            // Remove existing combination
            return prevCombinations.filter((_, index) => index !== existingIndex);
          } else {
            // Add new combination with priority based on current length + 1
            const newCombination = {
              labId: selectedLab._id,
              labName: selectedLab.name,
              labDepartmentId: department._id,
              departmentName: department.name,
              priority: prevCombinations.length + 1
            };
            return [...prevCombinations, newCombination];
          }
        });
      }

      // Also update selected departments for UI feedback
      setSelectedDepartments(prevSelected => {
        const isAlreadySelected = prevSelected.some(selectedDept => selectedDept._id === department._id);
        if (isAlreadySelected) {
          return prevSelected.filter(selectedDept => selectedDept._id !== department._id);
        } else {
          return [...prevSelected, department];
        }
      });
    } else {
      // Single select mode
      console.log('Department selected:', department.name, 'ID:', department._id);
      setSelectedDepartment(department);
    }
  };

  // Handle long press on department to enter multi-select mode
  const handleDepartmentLongPress = (department) => {
    if (isDragging) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setIsMultiSelectMode(true);
    setSelectedDepartments([department]);
    console.log('Entered multi-select mode with department:', department.name);
  };

  // Handle confirmation
  const handleConfirm = () => {
    if (isMultiSelectMode) {
      // Multi-select mode: confirm multiple selections
      if (currentStep === 'lab') {
        if (selectedLabs.length === 0) {
          const errorMsg = logError('Confirmation', 'No labs selected');
          setErrors(prev => ({ ...prev, confirmation: errorMsg }));
          return;
        }

        // For multi-select labs, we'll pass all selected labs to the callback
        console.log('Confirming multi-lab selection:', selectedLabs.map(lab => lab.name));
        onConfirmSelection(selectedLabs.map(lab => lab._id), null, selectedLabs.map(lab => lab.name), null);
        onClose();
      } else {
        // Department step in multi-select mode - use lab-department combinations
        if (labDepartmentCombinations.length === 0) {
          const errorMsg = logError('Confirmation', 'No lab-department combinations selected');
          setErrors(prev => ({ ...prev, confirmation: errorMsg }));
          return;
        }

        console.log('Confirming lab-department combinations:', labDepartmentCombinations);
        // Pass the lab-department combinations with priorities to the callback
        onConfirmSelection(labDepartmentCombinations);
        onClose();
      }
    } else {
      // Single select mode (legacy behavior)
      if (!selectedLab || !selectedDepartment) {
        const errorMsg = logError('Confirmation', 'No lab or department selected');
        setErrors(prev => ({ ...prev, confirmation: errorMsg }));
        return;
      }

      console.log('Confirming single selection - Lab:', selectedLab.name, 'Department:', selectedDepartment.name);
      onConfirmSelection(selectedLab._id, selectedDepartment._id, selectedLab.name, selectedDepartment.name);
      onClose();
    }
  };
  
  // Go back to lab selection
  const goBackToLabs = () => {
    setCurrentStep('lab');
    setSelectedDepartment(null);
    setSelectedDepartments([]);
    setLabDepartmentCombinations([]);
    setIsDragging(false);
  };

  // Exit multi-select mode
  const exitMultiSelectMode = () => {
    setIsMultiSelectMode(false);
    setSelectedLabs([]);
    setSelectedDepartments([]);
    setLabDepartmentCombinations([]);
  };

  // Update priorities after drag and drop
  const updateCombinationPriorities = (reorderedCombinations) => {
    const updatedCombinations = reorderedCombinations.map((combo, index) => ({
      ...combo,
      priority: index + 1
    }));
    setLabDepartmentCombinations(updatedCombinations);
  };

  // Start dragging
  const onDragStart = (item, index) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setDraggedItem(item);
    setDraggedItemIndex(index);
    setIsDragging(true);
  };

  // Handle pan gesture
  const onPanGestureEvent = (event) => {
    dragY.setValue(event.nativeEvent.translationY);

    // Calculate new position
    const currentItems = currentStep === 'lab' ? labs : departments;
    const itemHeight = 50; // Approximate height of each item
    const newPosition = Math.floor(draggedItemIndex + event.nativeEvent.translationY / itemHeight);

    if (newPosition >= 0 && newPosition < currentItems.length && newPosition !== draggedItemIndex) {
      // Reorder the items
      const updatedItems = [...currentItems];
      const [movedItem] = updatedItems.splice(draggedItemIndex, 1);
      updatedItems.splice(newPosition, 0, movedItem);

      if (currentStep === 'lab') {
        setLabs(updatedItems);
      } else {
        setDepartments(updatedItems);

        // If in multi-select mode and we're reordering departments,
        // update the priorities of lab-department combinations
        if (isMultiSelectMode && labDepartmentCombinations.length > 0) {
          updateCombinationPriorities(labDepartmentCombinations);
        }
      }

      setDraggedItemIndex(newPosition);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  // Handle end of drag
  const onPanGestureEnd = () => {
    setIsDragging(false);
    setDraggedItem(null);
    dragY.setValue(0);
  };

  // Render an item (lab or department)
  const renderItem = (item, index, isSelected) => {
    const isDraggingThis = isDragging && draggedItem && draggedItem._id === item._id;

    // Check if item is selected in multi-select mode
    const isMultiSelected = isMultiSelectMode && (
      (currentStep === 'lab' && selectedLabs.some(lab => lab._id === item._id)) ||
      (currentStep === 'department' && selectedDepartments.some(dept => dept._id === item._id))
    );

    const itemStyle = [
      styles.option,
      (isSelected || isMultiSelected) && styles.selectedItem,
      isDraggingThis && styles.draggingItem
    ];

    // Create a dynamic animated style for the dragged item
    const animatedStyle = isDraggingThis ? {
      transform: [{ translateY: dragY }],
      zIndex: 100,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    } : {};

    return (
      <Animated.View
        key={item._id}
        style={[itemStyle, animatedStyle]}
        ref={(ref) => {
          if (ref) {
            itemRefs.current[item._id] = ref;
          }
        }}
      >
        <View style={styles.optionRow}>
          <Text style={styles.indexText}>{index + 1}.</Text>

          {/* Multi-select checkbox */}
          {isMultiSelectMode && (
            <View style={styles.checkboxContainer}>
              <MaterialCommunityIcons
                name={isMultiSelected ? "checkbox-marked" : "checkbox-blank-outline"}
                size={24}
                color={isMultiSelected ? Colors.primary : Colors.lightText}
              />
            </View>
          )}

          <TouchableOpacity
            style={styles.itemContent}
            onPress={() => currentStep === 'lab' ? handleLabSelect(item) : handleDepartmentSelect(item)}
            onLongPress={() => currentStep === 'lab' ? handleLabLongPress(item) : handleDepartmentLongPress(item)}
            delayLongPress={500}
          >
            <Text style={[
              styles.optionText,
              (isSelected || isMultiSelected) && styles.selectedText
            ]}>{item.name}</Text>
          </TouchableOpacity>

          <PanGestureHandler
            onGestureEvent={onPanGestureEvent}
            onEnded={onPanGestureEnd}
            onBegan={() => onDragStart(item, index)}
          >
            <View style={styles.dragHandle}>
              <Text style={styles.dragHandleText}>≡</Text>
            </View>
          </PanGestureHandler>
        </View>
      </Animated.View>
    );
  };

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <View style={styles.overlay}>
          <View style={styles.modalView}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {currentStep === 'lab'
                  ? (isMultiSelectMode
                      ? `Select Labs (${selectedLabs.length} selected)`
                      : 'Select Lab')
                  : (isMultiSelectMode
                      ? `Select Departments (${selectedDepartments.length} selected)`
                      : `Select Department for ${selectedLab ? selectedLab.name : ''}`)}
              </Text>

              {/* Exit multi-select mode button */}
              {isMultiSelectMode && (
                <TouchableOpacity
                  style={styles.exitMultiSelectButton}
                  onPress={exitMultiSelectMode}
                >
                  <MaterialCommunityIcons name="close" size={20} color={Colors.lightText} />
                </TouchableOpacity>
              )}
            </View>

            {isLoading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#007BFF" />
                <Text style={styles.loaderText}>
                  {currentStep === 'lab' ? 'Loading labs...' : 'Loading departments...'}
                </Text>
              </View>
            ) : currentStep === 'lab' ? (
              // Lab Selection View
              labs.length > 0 ? (
                <View style={styles.optionsContainer}>
                  {labs.map((lab, index) => 
                    renderItem(lab, index, selectedLab && selectedLab._id === lab._id)
                  )}
                </View>
              ) : (
                <Text style={styles.noDataText}>
                  {errors.labsFetch ? 'Error loading labs' : 'No labs available'}
                </Text>
              )
            ) : (
              // Department Selection View
              <View>
                {departments.length > 0 ? (
                  <View style={styles.optionsContainer}>
                    {departments.map((dept, index) =>
                      renderItem(dept, index, selectedDepartment && selectedDepartment._id === dept._id)
                    )}
                  </View>
                ) : (
                  <Text style={styles.noDataText}>
                    {errors.departmentsFetch ? 'Error loading departments' : 'No departments available'}
                  </Text>
                )}

                {/* Show selected lab-department combinations in multi-select mode */}
                {isMultiSelectMode && labDepartmentCombinations.length > 0 && (
                  <View style={styles.combinationsContainer}>
                    <Text style={styles.combinationsTitle}>Selected Combinations (Priority Order):</Text>
                    {labDepartmentCombinations
                      .sort((a, b) => a.priority - b.priority)
                      .map((combo) => (
                        <View key={`${combo.labId}-${combo.labDepartmentId}`} style={styles.combinationItem}>
                          <Text style={styles.priorityNumber}>{combo.priority}.</Text>
                          <View style={styles.combinationDetails}>
                            <Text style={styles.combinationText}>
                              {combo.labName} → {combo.departmentName}
                            </Text>
                          </View>
                          <TouchableOpacity
                            style={styles.removeCombinationButton}
                            onPress={() => {
                              setLabDepartmentCombinations(prev =>
                                prev.filter(c => !(c.labId === combo.labId && c.labDepartmentId === combo.labDepartmentId))
                                  .map((c, idx) => ({ ...c, priority: idx + 1 }))
                              );
                            }}
                          >
                            <MaterialCommunityIcons name="close-circle" size={20} color={Colors.lightText} />
                          </TouchableOpacity>
                        </View>
                      ))}
                  </View>
                )}
              </View>
            )}

            <View style={styles.buttonRow}>
              {currentStep === 'lab' ? (
                isMultiSelectMode ? (
                  <>
                    <TouchableOpacity
                      style={[styles.button, styles.backButton]}
                      onPress={exitMultiSelectMode}
                    >
                      <Text style={[styles.buttonText]}>Cancel</Text>
                    </TouchableOpacity>
                    <View style={styles.buttonDivider} />
                    <TouchableOpacity
                      style={[
                        styles.button,
                        styles.primaryButton,
                        (selectedLabs.length === 0 || isDragging) && styles.disabledButton
                      ]}
                      onPress={handleConfirm}
                      disabled={selectedLabs.length === 0 || isDragging}
                    >
                      <Text style={[styles.buttonText, styles.primaryButtonText]}>
                        Confirm ({selectedLabs.length})
                      </Text>
                    </TouchableOpacity>
                  </>
                ) : (
                  <TouchableOpacity
                    style={[styles.button, styles.secondaryButton]}
                    onPress={onClose}
                  >
                    <Text style={[styles.buttonText, styles.secondaryButtonText]}>Cancel</Text>
                  </TouchableOpacity>
                )
              ) : (
                <>
                  <TouchableOpacity
                    style={[styles.button, styles.backButton]}
                    onPress={goBackToLabs}
                  >
                    <Text style={[styles.buttonText]}>Back</Text>
                  </TouchableOpacity>
                  <View style={styles.buttonDivider} />
                  <TouchableOpacity
                    style={[
                      styles.button,
                      styles.primaryButton,
                      (isMultiSelectMode
                        ? (labDepartmentCombinations.length === 0 || isDragging)
                        : (!selectedDepartment || isDragging)) && styles.disabledButton
                    ]}
                    onPress={handleConfirm}
                    disabled={isMultiSelectMode
                      ? (labDepartmentCombinations.length === 0 || isDragging)
                      : (!selectedDepartment || isDragging)}
                  >
                    <Text style={[styles.buttonText, styles.primaryButtonText]}>
                      {isMultiSelectMode
                        ? `Confirm (${labDepartmentCombinations.length})`
                        : 'Confirm'}
                    </Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </View>
        </View>
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  modalView: {
    width: "80%",
    backgroundColor: Colors.background,
    borderRadius: 10,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingBottom: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Roboto_bold',
    textAlign: 'center',
    color: Colors.black,
  },
  optionsContainer: {
    maxHeight: 300,
  },
  option: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    backgroundColor: Colors.background,
  },
  selectedItem: {
    backgroundColor: '#E8F0FE', // Light blue background for selected items
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  indexText: {
    color: 'black',
    fontFamily: 'Roboto_bold',
    fontSize: 18,
    width: 30,
    textAlign: 'center',
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'Roboto',
    color: Colors.black,
  },
  selectedText: {
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
  },
  dragHandle: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 2,
    borderLeftColor: Colors.border,
    marginLeft: 5,
  },
  dragHandleText: {
    fontSize: 24,
    color: Colors.lightText,
  },
  noDataText: {
    padding: 20,
    textAlign: 'center',
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    padding: 12,
    borderRadius: 5,
    flex: 1,
    alignItems: "center",
  },
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.lightText,
  },
  backButton: {
    backgroundColor: Colors.lightText,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
  },
  buttonText: {
    color: Colors.background,
    fontFamily: 'Roboto_bold',
  },
  secondaryButtonText: {
    color: "white",
  },
  primaryButtonText: {
    color: "white",
  },
  buttonDivider: {
    width: 10,
  },
  loaderContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 10,
    color: Colors.lightText,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  draggingItem: {
    backgroundColor: "#F5F5F5",
  },
  checkboxContainer: {
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  exitMultiSelectButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  combinationsContainer: {
    marginTop: 15,
    padding: 10,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  combinationsTitle: {
    fontSize: 14,
    fontFamily: 'Roboto_bold',
    color: Colors.black,
    marginBottom: 10,
  },
  combinationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 5,
    backgroundColor: Colors.background,
    borderRadius: 5,
    marginBottom: 5,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  priorityNumber: {
    fontSize: 14,
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
    width: 25,
    textAlign: 'center',
  },
  combinationDetails: {
    flex: 1,
    marginLeft: 10,
  },
  combinationText: {
    fontSize: 14,
    fontFamily: 'Roboto',
    color: Colors.black,
  },
  removeCombinationButton: {
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LabSelectionModal;