import React, { useState, useEffect, useCallback, useRef } from "react";
import { View, Text, TouchableOpacity, Modal, StyleSheet, ActivityIndicator, Animated } from "react-native";
import Constants from 'expo-constants';
import { Colors } from "../../constants/colors";
import { GestureHandlerRootView, PanGestureHandler } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

// Enhanced error logger
const logError = (context, error) => {
  const errorMessage = `ERROR [${context}]: ${error.message || error}`;
  
  console.log('\n' + '='.repeat(80));
  console.log(errorMessage);
  console.log('Stack:', error.stack || 'No stack trace available');
  
  if (error.response) {
    console.log('Response data:', error.response.data);
    console.log('Response status:', error.response.status);
  }
  
  if (error.request) {
    console.log('Request:', error.request);
  }
  
  if (error.config) {
    console.log('Request URL:', error.config.url);
    console.log('Request method:', error.config.method);
  }
  
  console.log('='.repeat(80) + '\n');
  
  return errorMessage;
};

const LabSelectionModal = ({
  isVisible,
  onClose,
  onConfirmSelection,
}) => {
  // Simple, unified state
  const [labs, setLabs] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [selectedLab, setSelectedLab] = useState(null);
  const [labDepartmentCombinations, setLabDepartmentCombinations] = useState([]);
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // UI state - simple 3-step flow
  const [currentView, setCurrentView] = useState('labs'); // 'labs' | 'departments' | 'review'

  // Drag and drop state
  const [isDragging, setIsDragging] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);

  // Animation values
  const dragY = useRef(new Animated.Value(0)).current;

  const fetchLabs = useCallback(() => {
    if (!BASE_URL) {
      const errorMsg = logError('Environment', 'BASE_URL is undefined. Check your environment configuration.');
      setErrors(prev => ({ ...prev, baseUrl: errorMsg }));
      return;
    }
    
    setIsLoading(true);
    console.log(`Fetching labs from: ${BASE_URL}/api/forensic/lab`);
    
    fetch(`${BASE_URL}/api/forensic/lab`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Labs data received:', JSON.stringify(data, null, 2));
        if (data.status === "success") {
          setLabs(data.data);
        } else {
          throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
        }
      })
      .catch((error) => {
        const errorMsg = logError('Fetching Labs', error);
        setErrors(prev => ({ ...prev, labsFetch: errorMsg }));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Fetch departments for the selected lab - using useCallback
  const fetchDepartments = useCallback((labId) => {
    if (!labId) {
      const errorMsg = logError('Fetch Departments', 'No lab ID provided');
      setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
      return;
    }
    
    setIsLoading(true);
    console.log(`Fetching departments for lab ID ${labId} from: ${BASE_URL}/api/forensic/lab/${labId}/department`);
    
    fetch(`${BASE_URL}/api/forensic/lab/${labId}/department`)
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Departments data received:', JSON.stringify(data, null, 2));
        if (data.status === "success") {
          setDepartments(data.data);
          setCurrentStep('department');
        } else {
          throw new Error(`API returned error: ${data.message || 'Unknown error'}`);
        }
      })
      .catch((error) => {
        const errorMsg = logError('Fetching Departments', error);
        setErrors(prev => ({ ...prev, departmentsFetch: errorMsg }));
        // Even in case of error, switch to department step to show error
        setCurrentStep('department');
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);
  
  // Initialize when modal becomes visible
  useEffect(() => {
    if (isVisible) {
      setCurrentView('labs');
      fetchLabs();
    } else {
      // Reset state when modal is closed
      setSelectedLab(null);
      setDepartments([]);
      setLabDepartmentCombinations([]);
      setCurrentView('labs');
      setErrors({});
      setIsDragging(false);
    }
  }, [isVisible, fetchLabs]);

  // Simple lab selection - always proceed to departments
  const handleLabSelect = (lab) => {
    if (isDragging) return;

    console.log('Lab selected:', lab.name, 'ID:', lab._id);
    setSelectedLab(lab);
    setCurrentView('departments');
    fetchDepartments(lab._id);
  };

  // Simple department selection - add to combinations or go to review
  const handleDepartmentSelect = (department) => {
    if (isDragging) return;

    if (!selectedLab) return;

    // Check if this combination already exists
    const existingIndex = labDepartmentCombinations.findIndex(
      combo => combo.labId === selectedLab._id && combo.labDepartmentId === department._id
    );

    if (existingIndex >= 0) {
      // Remove existing combination
      setLabDepartmentCombinations(prev =>
        prev.filter((_, index) => index !== existingIndex)
          .map((combo, index) => ({ ...combo, priority: index + 1 }))
      );
    } else {
      // Add new combination
      const newCombination = {
        labId: selectedLab._id,
        labName: selectedLab.name,
        labDepartmentId: department._id,
        departmentName: department.name,
        priority: labDepartmentCombinations.length + 1
      };
      setLabDepartmentCombinations(prev => [...prev, newCombination]);
    }

    console.log('Department toggled:', department.name);
  };

  // Go to review/combinations view
  const goToReview = () => {
    if (labDepartmentCombinations.length > 0) {
      setCurrentView('review');
    }
  };

  // Simple confirmation - always send combinations
  const handleConfirm = () => {
    if (labDepartmentCombinations.length === 0) {
      const errorMsg = logError('Confirmation', 'No lab-department combinations selected');
      setErrors(prev => ({ ...prev, confirmation: errorMsg }));
      return;
    }

    console.log('Confirming lab-department combinations:', labDepartmentCombinations);

    // Always send as multi-lab-department format (even for single selection)
    onConfirmSelection({
      type: 'multi-lab-department',
      data: labDepartmentCombinations
    });
    onClose();
  };
  
  // Navigation functions
  const goBackToLabs = () => {
    setCurrentView('labs');
    setSelectedLab(null);
    setDepartments([]);
    setLabDepartmentCombinations([]);
    setIsDragging(false);
  };

  const goBackToDepartments = () => {
    setCurrentView('departments');
    setIsDragging(false);
  };

  // Start dragging
  const onDragStart = (index) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setDraggedItemIndex(index);
    setIsDragging(true);
  };

  // Handle pan gesture for reordering combinations
  const onPanGestureEvent = (event) => {
    dragY.setValue(event.nativeEvent.translationY);

    // Only allow reordering in review view for combinations
    if (currentView === 'review' && labDepartmentCombinations.length > 1) {
      const itemHeight = 60; // Height of combination items
      const newPosition = Math.floor(draggedItemIndex + event.nativeEvent.translationY / itemHeight);

      if (newPosition >= 0 && newPosition < labDepartmentCombinations.length && newPosition !== draggedItemIndex) {
        // Reorder combinations and update priorities
        const updatedCombinations = [...labDepartmentCombinations];
        const [movedItem] = updatedCombinations.splice(draggedItemIndex, 1);
        updatedCombinations.splice(newPosition, 0, movedItem);

        // Update priorities based on new order
        const reorderedWithPriorities = updatedCombinations.map((combo, index) => ({
          ...combo,
          priority: index + 1
        }));

        setLabDepartmentCombinations(reorderedWithPriorities);
        setDraggedItemIndex(newPosition);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    }
  };

  // Handle end of drag
  const onPanGestureEnd = () => {
    setIsDragging(false);
    setDraggedItem(null);
    dragY.setValue(0);
  };

  // Render lab item
  const renderLabItem = (lab, index) => {
    const isSelected = selectedLab && selectedLab._id === lab._id;

    return (
      <TouchableOpacity
        key={lab._id}
        style={[styles.option, isSelected && styles.selectedItem]}
        onPress={() => handleLabSelect(lab)}
      >
        <View style={styles.optionRow}>
          <Text style={styles.indexText}>{index + 1}.</Text>
          <Text style={[
            styles.optionText,
            isSelected && styles.selectedText
          ]}>{lab.name}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Render department item
  const renderDepartmentItem = (department, index) => {
    const isSelected = labDepartmentCombinations.some(
      combo => combo.labDepartmentId === department._id
    );

    return (
      <TouchableOpacity
        key={department._id}
        style={[styles.option, isSelected && styles.selectedItem]}
        onPress={() => handleDepartmentSelect(department)}
      >
        <View style={styles.optionRow}>
          <Text style={styles.indexText}>{index + 1}.</Text>
          <Text style={[
            styles.optionText,
            isSelected && styles.selectedText
          ]}>{department.name}</Text>
          {isSelected && (
            <MaterialCommunityIcons
              name="check-circle"
              size={20}
              color={Colors.primary}
              style={styles.checkIcon}
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  // Render combination item (for review view)
  const renderCombinationItem = (combo, index) => {
    const isDraggingThis = isDragging && draggedItemIndex === index;

    const animatedStyle = isDraggingThis ? {
      transform: [{ translateY: dragY }],
      zIndex: 100,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    } : {};

    return (
      <Animated.View
        key={`${combo.labId}-${combo.labDepartmentId}`}
        style={[styles.combinationItem, animatedStyle]}
      >
        <Text style={styles.priorityNumber}>{combo.priority}.</Text>
        <View style={styles.combinationDetails}>
          <Text style={styles.combinationText}>
            {combo.labName} → {combo.departmentName}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.removeCombinationButton}
          onPress={() => {
            setLabDepartmentCombinations(prev =>
              prev.filter(c => !(c.labId === combo.labId && c.labDepartmentId === combo.labDepartmentId))
                .map((c, idx) => ({ ...c, priority: idx + 1 }))
            );
          }}
        >
          <MaterialCommunityIcons name="close-circle" size={20} color={Colors.lightText} />
        </TouchableOpacity>

        {/* Drag handle for reordering */}
        <PanGestureHandler
          onGestureEvent={onPanGestureEvent}
          onEnded={onPanGestureEnd}
          onBegan={() => onDragStart(index)}
        >
          <View style={styles.dragHandle}>
            <Text style={styles.dragHandleText}>≡</Text>
          </View>
        </PanGestureHandler>
      </Animated.View>
    );
  };

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <View style={styles.overlay}>
          <View style={styles.modalView}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {currentView === 'labs' && 'Select Lab'}
                {currentView === 'departments' && `Select Departments for ${selectedLab?.name || ''}`}
                {currentView === 'review' && `Review Selections (${labDepartmentCombinations.length})`}
              </Text>
            </View>

            {isLoading ? (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#007BFF" />
                <Text style={styles.loaderText}>
                  {currentView === 'labs' ? 'Loading labs...' : 'Loading departments...'}
                </Text>
              </View>
            ) : (
              <View>
                {/* Labs View */}
                {currentView === 'labs' && (
                  labs.length > 0 ? (
                    <View style={styles.optionsContainer}>
                      {labs.map((lab, index) => renderLabItem(lab, index))}
                    </View>
                  ) : (
                    <Text style={styles.noDataText}>
                      {errors.labsFetch ? 'Error loading labs' : 'No labs available'}
                    </Text>
                  )
                )}

                {/* Departments View */}
                {currentView === 'departments' && (
                  <View>
                    {departments.length > 0 ? (
                      <View style={styles.optionsContainer}>
                        {departments.map((dept, index) => renderDepartmentItem(dept, index))}
                      </View>
                    ) : (
                      <Text style={styles.noDataText}>
                        {errors.departmentsFetch ? 'Error loading departments' : 'No departments available'}
                      </Text>
                    )}

                    {/* Show current selections */}
                    {labDepartmentCombinations.length > 0 && (
                      <View style={styles.currentSelectionsContainer}>
                        <Text style={styles.currentSelectionsTitle}>
                          Current Selections ({labDepartmentCombinations.length}):
                        </Text>
                        {labDepartmentCombinations.map((combo, index) => (
                          <Text key={`${combo.labId}-${combo.labDepartmentId}`} style={styles.currentSelectionText}>
                            {index + 1}. {combo.departmentName}
                          </Text>
                        ))}
                      </View>
                    )}
                  </View>
                )}

                {/* Review View */}
                {currentView === 'review' && (
                  <View style={styles.combinationsContainer}>
                    <Text style={styles.combinationsTitle}>
                      Lab-Department Combinations (Drag to reorder priority):
                    </Text>
                    {labDepartmentCombinations
                      .sort((a, b) => a.priority - b.priority)
                      .map((combo, index) => renderCombinationItem(combo, index))}
                  </View>
                )}
              </View>
            )}

            <View style={styles.buttonRow}>
              {currentView === 'labs' && (
                <TouchableOpacity
                  style={[styles.button, styles.secondaryButton]}
                  onPress={onClose}
                >
                  <Text style={[styles.buttonText, styles.secondaryButtonText]}>Cancel</Text>
                </TouchableOpacity>
              )}

              {currentView === 'departments' && (
                <>
                  <TouchableOpacity
                    style={[styles.button, styles.backButton]}
                    onPress={goBackToLabs}
                  >
                    <Text style={[styles.buttonText]}>Back to Labs</Text>
                  </TouchableOpacity>
                  <View style={styles.buttonDivider} />
                  <TouchableOpacity
                    style={[
                      styles.button,
                      styles.primaryButton,
                      labDepartmentCombinations.length === 0 && styles.disabledButton
                    ]}
                    onPress={goToReview}
                    disabled={labDepartmentCombinations.length === 0}
                  >
                    <Text style={[styles.buttonText, styles.primaryButtonText]}>
                      Review ({labDepartmentCombinations.length})
                    </Text>
                  </TouchableOpacity>
                </>
              )}

              {currentView === 'review' && (
                <>
                  <TouchableOpacity
                    style={[styles.button, styles.backButton]}
                    onPress={goBackToDepartments}
                  >
                    <Text style={[styles.buttonText]}>Back to Departments</Text>
                  </TouchableOpacity>
                  <View style={styles.buttonDivider} />
                  <TouchableOpacity
                    style={[
                      styles.button,
                      styles.primaryButton,
                      (labDepartmentCombinations.length === 0 || isDragging) && styles.disabledButton
                    ]}
                    onPress={handleConfirm}
                    disabled={labDepartmentCombinations.length === 0 || isDragging}
                  >
                    <Text style={[styles.buttonText, styles.primaryButtonText]}>
                      Confirm ({labDepartmentCombinations.length})
                    </Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </View>
        </View>
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  modalView: {
    width: "80%",
    backgroundColor: Colors.background,
    borderRadius: 10,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingBottom: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    position: 'relative',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Roboto_bold',
    textAlign: 'center',
    color: Colors.black,
  },
  optionsContainer: {
    maxHeight: 300,
  },
  option: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    backgroundColor: Colors.background,
  },
  selectedItem: {
    backgroundColor: '#E8F0FE', // Light blue background for selected items
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  indexText: {
    color: 'black',
    fontFamily: 'Roboto_bold',
    fontSize: 18,
    width: 30,
    textAlign: 'center',
  },
  itemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 16,
    fontFamily: 'Roboto',
    color: Colors.black,
  },
  selectedText: {
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
  },
  dragHandle: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 2,
    borderLeftColor: Colors.border,
    marginLeft: 5,
  },
  dragHandleText: {
    fontSize: 24,
    color: Colors.lightText,
  },
  noDataText: {
    padding: 20,
    textAlign: 'center',
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    padding: 12,
    borderRadius: 5,
    flex: 1,
    alignItems: "center",
  },
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.lightText,
  },
  backButton: {
    backgroundColor: Colors.lightText,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
  },
  buttonText: {
    color: Colors.background,
    fontFamily: 'Roboto_bold',
  },
  secondaryButtonText: {
    color: "white",
  },
  primaryButtonText: {
    color: "white",
  },
  buttonDivider: {
    width: 10,
  },
  loaderContainer: {
    padding: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 10,
    color: Colors.lightText,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  draggingItem: {
    backgroundColor: "#F5F5F5",
  },
  checkboxContainer: {
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  exitMultiSelectButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  combinationsContainer: {
    marginTop: 15,
    padding: 10,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  combinationsTitle: {
    fontSize: 14,
    fontFamily: 'Roboto_bold',
    color: Colors.black,
    marginBottom: 10,
  },
  combinationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 5,
    backgroundColor: Colors.background,
    borderRadius: 5,
    marginBottom: 5,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  priorityNumber: {
    fontSize: 14,
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
    width: 25,
    textAlign: 'center',
  },
  combinationDetails: {
    flex: 1,
    marginLeft: 10,
  },
  combinationText: {
    fontSize: 14,
    fontFamily: 'Roboto',
    color: Colors.black,
  },
  removeCombinationButton: {
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkIcon: {
    marginLeft: 10,
  },
  currentSelectionsContainer: {
    marginTop: 15,
    padding: 10,
    backgroundColor: '#F0F8FF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  currentSelectionsTitle: {
    fontSize: 14,
    fontFamily: 'Roboto_bold',
    color: Colors.primary,
    marginBottom: 8,
  },
  currentSelectionText: {
    fontSize: 12,
    fontFamily: 'Roboto',
    color: Colors.black,
    marginBottom: 2,
  },
});

export default LabSelectionModal;